{"Files": [{"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\3a538760-806e-469a-ac21-447df17b3421_北京励控-五星售后(1).pdf", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\3a538760-806e-469a-ac21-447df17b3421_北京励控-五星售后(1).pdf"}, {"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\5ceeb27a-e384-4f58-a2f6-8adb3517fe15_北京励控-五星售后(1)(1).pdf", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\5ceeb27a-e384-4f58-a2f6-8adb3517fe15_北京励控-五星售后(1)(1).pdf"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.WatermarkCore.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.WatermarkCore.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.WatermarkCore.props", "PackagePath": "build\\WatermarkCore.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.WatermarkCore.props", "PackagePath": "buildMultiTargeting\\WatermarkCore.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.WatermarkCore.props", "PackagePath": "buildTransitive\\WatermarkCore.props"}], "ElementsToRemove": []}