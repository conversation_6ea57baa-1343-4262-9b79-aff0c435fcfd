<template>
  <div class="file-manager">
    <div class="toolbar">
      <el-button type="primary" @click="showUploadDialog = true">
        <el-icon><Upload /></el-icon>
        上传文件
      </el-button>
      <el-button @click="showCreateFolderDialog = true">
        <el-icon><FolderAdd /></el-icon>
        新建文件夹
      </el-button>
    </div>

    <div class="file-tree">
      <el-tree
        :data="fileTree"
        :props="treeProps"
        node-key="id"
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeRightClick"
        :expand-on-click-node="false"
      >
        <template #default="{ node, data }">
          <span class="tree-node">
            <el-icon v-if="data.type === 'folder'"><Folder /></el-icon>
            <el-icon v-else-if="data.type === 'image'"><Picture /></el-icon>
            <el-icon v-else-if="data.type === 'pdf'"><Document /></el-icon>
            <el-icon v-else-if="data.type === 'word'"><Document /></el-icon>
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>

    <!-- 上传文件对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文件" width="500px">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :on-change="handleFileSelect"
        :accept="acceptedFileTypes"
        drag
        multiple
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">支持 jpg/png/gif/pdf/docx 格式文件</div>
        </template>
      </el-upload>
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpload">确定</el-button>
      </template>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog v-model="showCreateFolderDialog" title="新建文件夹" width="400px">
      <el-form :model="folderForm" label-width="80px">
        <el-form-item label="文件夹名">
          <el-input v-model="folderForm.name" placeholder="请输入文件夹名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolder">确定</el-button>
      </template>
    </el-dialog>

    <!-- 重命名文件夹对话框 -->
    <el-dialog v-model="showRenameFolderDialog" title="重命名文件夹" width="400px">
      <el-form :model="renameForm" label-width="80px">
        <el-form-item label="新名称">
          <el-input v-model="renameForm.newName" placeholder="请输入新的文件夹名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRenameFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmRename">确定</el-button>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      :virtual-ref="contextMenuVirtualRef"
      virtual-triggering
      @command="handleContextMenuCommand"
    >
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="rename" v-if="contextMenuData?.type === 'folder' && contextMenuData?.id !== 'root'">
            <el-icon><Edit /></el-icon>
            重命名
          </el-dropdown-item>
          <el-dropdown-item command="delete" v-if="contextMenuData?.type === 'folder' && contextMenuData?.id !== 'root'">
            <el-icon><Delete /></el-icon>
            删除文件夹
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { Upload, FolderAdd, Folder, Picture, Document, UploadFilled, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useFileStore } from '../stores/fileStore'
import { FileTypeDetector } from '../utils/watermarkUtils'

const emit = defineEmits(['file-selected'])
const fileStore = useFileStore()

// 响应式数据
const showUploadDialog = ref(false)
const showCreateFolderDialog = ref(false)
const showRenameFolderDialog = ref(false)
const uploadRef = ref()
const selectedFiles = ref([])
const contextMenuRef = ref()
const contextMenuVirtualRef = ref()
const contextMenuData = ref(null)

const folderForm = reactive({
  name: '',
})

const renameForm = reactive({
  newName: '',
  folderId: '',
})

// 使用store中的文件树
const fileTree = computed(() => fileStore.fileTree)

const treeProps = {
  children: 'children',
  label: 'label',
}

const acceptedFileTypes = '.jpg,.jpeg,.png,.gif,.pdf,.docx,.doc'

// 方法
const handleFileSelect = (file) => {
  selectedFiles.value.push(file)
}

const handleUpload = async () => {
  if (selectedFiles.value.length === 0) {
    return
  }

  let successCount = 0
  let errorCount = 0

  for (const file of selectedFiles.value) {
    try {
      // 验证文件
      const validation = await FileTypeDetector.validateFile(file.raw)

      if (!validation.isValid) {
        const errors = validation.errors.filter((e) => !e.startsWith('警告'))
        if (errors.length > 0) {
          ElMessage.error(`${file.name}: ${errors.join(', ')}`)
          errorCount++
          continue
        }
      }

      // 显示警告（如果有）
      if (validation.warnings.length > 0) {
        ElMessage.warning(`${file.name}: ${validation.warnings.join(', ')}`)
      }

      const fileType = FileTypeDetector.getFileType(file.name)
      await fileStore.addFile({
        name: file.name,
        type: fileType,
        file: file.raw,
        size: file.size,
        folderId: fileStore.currentFolder
      })

      successCount++
    } catch (error) {
      console.error(`文件上传失败: ${file.name}`, error)
      errorCount++
    }
  }

  // 显示结果摘要
  if (successCount > 0) {
    ElMessage.success(`成功上传 ${successCount} 个文件`)
  }
  if (errorCount > 0) {
    ElMessage.error(`${errorCount} 个文件上传失败`)
  }

  selectedFiles.value = []
  showUploadDialog.value = false
}

const createFolder = async () => {
  if (!folderForm.name.trim()) {
    return
  }

  try {
    await fileStore.addFolder(folderForm.name)
    folderForm.name = ''
    showCreateFolderDialog.value = false
  } catch (error) {
    // 错误已在store中处理
  }
}

const handleNodeClick = (data) => {
  if (data.type !== 'folder' && data.file) {
    emit('file-selected', data.file)
  }
}

// 右键菜单处理
const handleNodeRightClick = (event, data) => {
  event.preventDefault()
  event.stopPropagation()

  // 只对文件夹显示右键菜单，且不是根文件夹
  if (data.type === 'folder' && data.id !== 'root') {
    contextMenuData.value = data

    // 获取鼠标位置
    const mouseX = event.clientX
    const mouseY = event.clientY

    // 创建虚拟引用，确保菜单在鼠标位置显示
    contextMenuVirtualRef.value = {
      getBoundingClientRect() {
        return {
          x: mouseX,
          y: mouseY,
          width: 0,
          height: 0,
          top: mouseY,
          left: mouseX,
          right: mouseX,
          bottom: mouseY,
        }
      }
    }

    // 使用 nextTick 确保虚拟引用已设置
    nextTick(() => {
      contextMenuRef.value?.handleOpen()
    })
  }
}

// 右键菜单命令处理
const handleContextMenuCommand = async (command) => {
  if (!contextMenuData.value) return

  const folder = contextMenuData.value

  switch (command) {
    case 'rename':
      showRenameDialog(folder)
      break
    case 'delete':
      await deleteFolder(folder)
      break
  }

  contextMenuData.value = null
}

// 显示重命名对话框
const showRenameDialog = (folder) => {
  renameForm.folderId = folder.id
  renameForm.newName = folder.label
  showRenameFolderDialog.value = true
}

// 确认重命名
const confirmRename = async () => {
  if (!renameForm.newName.trim()) {
    ElMessage.warning('请输入新的文件夹名称')
    return
  }

  try {
    await fileStore.renameFolder(renameForm.folderId, renameForm.newName.trim())
    showRenameFolderDialog.value = false
    renameForm.newName = ''
    renameForm.folderId = ''
  } catch (error) {
    // 错误已在store中处理
  }
}

// 删除文件夹
const deleteFolder = async (folder) => {
  try {
    // 首先尝试普通删除
    await fileStore.deleteFolder(folder.id, false)
  } catch (error) {
    // 如果删除失败，检查是否是因为文件夹不为空
    if (error.message && error.message.includes('文件夹不为空')) {
      try {
        // 提示用户确认递归删除
        await ElMessageBox.confirm(
          `文件夹"${folder.label}"不为空，是否删除该文件夹及其所有内容？此操作不可恢复！`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        // 用户确认后进行递归删除
        await fileStore.deleteFolder(folder.id, true)
      } catch (confirmError) {
        if (confirmError !== 'cancel') {
          // 递归删除失败的错误已在store中处理
        }
      }
    } else {
      // 其他错误已在store中处理
    }
  }
}
</script>

<style scoped>
.file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.file-tree {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}
</style>
