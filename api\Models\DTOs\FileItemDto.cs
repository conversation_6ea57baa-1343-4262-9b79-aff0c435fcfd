namespace WatermarkCore.Models.DTOs
{
    /// <summary>
    /// 文件数据传输对象
    /// </summary>
    public class FileItemDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string OriginalName { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
        public long Size { get; set; }
        public string Extension { get; set; } = string.Empty;
        public string FolderId { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsWatermarked { get; set; }
        public DateTime? WatermarkedAt { get; set; }
        public string Status { get; set; } = "Normal";
        public string? Tags { get; set; }
        public string? Metadata { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 文件上传请求DTO
    /// </summary>
    public class UploadFileDto
    {
        public IFormFile File { get; set; } = null!;
        public string FolderId { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Tags { get; set; }
    }

    /// <summary>
    /// 更新文件请求DTO
    /// </summary>
    public class UpdateFileDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Tags { get; set; }
    }

    /// <summary>
    /// 移动文件请求DTO
    /// </summary>
    public class MoveFileDto
    {
        public string TargetFolderId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 文件搜索请求DTO
    /// </summary>
    public class SearchFileDto
    {
        public string? Keyword { get; set; }
        public string? FileType { get; set; }
        public string? FolderId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    /// <summary>
    /// 文件搜索结果DTO
    /// </summary>
    public class SearchFileResultDto
    {
        public List<FileItemDto> Files { get; set; } = new List<FileItemDto>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 批量操作请求DTO
    /// </summary>
    public class BatchOperationDto
    {
        public List<string> FileIds { get; set; } = new List<string>();
        public string Operation { get; set; } = string.Empty; // delete, move, tag
        public string? TargetFolderId { get; set; }
        public string? Tags { get; set; }
    }
}
