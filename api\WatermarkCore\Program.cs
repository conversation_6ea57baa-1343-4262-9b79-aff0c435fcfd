using Microsoft.EntityFrameworkCore;
using WatermarkCore.Data;
using WatermarkCore.Services;
using WatermarkCore.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// 配置数据库
builder.Services.AddDbContext<WatermarkDbContext>(options =>
    options.UseMySql(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        new MySqlServerVersion(new Version(5, 7, 44))
    ));

// 注册服务
builder.Services.AddScoped<IFileStorageService, FileStorageService>();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowVueApp", policy =>
    {
        policy.WithOrigins("http://localhost:5173", "http://localhost:5174", "http://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// 配置文件上传大小限制
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 104857600; // 100MB
});

builder.Services.AddControllers(options =>
{
    // 配置模型绑定大小限制
    options.ModelBindingMessageProvider.SetValueMustNotBeNullAccessor(_ => "值不能为空");
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 添加全局异常处理中间件
app.UseMiddleware<ExceptionHandlingMiddleware>();

// 添加文件上传中间件
app.UseMiddleware<FileUploadMiddleware>();

// 启用CORS
app.UseCors("AllowVueApp");

// 启用静态文件服务（用于文件上传）
app.UseStaticFiles();

app.UseAuthorization();

app.MapControllers();

// 确保数据库已创建
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<WatermarkDbContext>();
    context.Database.EnsureCreated();
}

app.Run();
