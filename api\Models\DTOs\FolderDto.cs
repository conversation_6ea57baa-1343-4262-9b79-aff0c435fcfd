namespace WatermarkCore.Models.DTOs
{
    /// <summary>
    /// 文件夹数据传输对象
    /// </summary>
    public class FolderDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? ParentId { get; set; }
        public string Path { get; set; } = string.Empty;
        public int Level { get; set; }
        public bool IsSystem { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public List<FolderDto> Children { get; set; } = new List<FolderDto>();
        public List<FileItemDto> Files { get; set; } = new List<FileItemDto>();
    }

    /// <summary>
    /// 创建文件夹请求DTO
    /// </summary>
    public class CreateFolderDto
    {
        public string Name { get; set; } = string.Empty;
        public string? ParentId { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// 更新文件夹请求DTO
    /// </summary>
    public class UpdateFolderDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    /// <summary>
    /// 移动文件夹请求DTO
    /// </summary>
    public class MoveFolderDto
    {
        public string TargetParentId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 文件夹树节点DTO
    /// </summary>
    public class FolderTreeNodeDto
    {
        public string Id { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string Type { get; set; } = "folder";
        public List<FolderTreeNodeDto> Children { get; set; } = new List<FolderTreeNodeDto>();
    }
}
