﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2025\08\04\3a538760-806e-469a-ac21-447df17b3421_北京励控-五星售后(1).pdf'))">
      <SourceType>Package</SourceType>
      <SourceId>WatermarkCore</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/WatermarkCore</BasePath>
      <RelativePath>uploads/2025/08/04/3a538760-806e-469a-ac21-447df17b3421_北京励控-五星售后(1).pdf</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>f58nbr0wrl</Fingerprint>
      <Integrity>/T9PKiaDPuX0FHrsP2yQnMdzSy5igbrsR9QsDUQ7wAg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2025\08\04\3a538760-806e-469a-ac21-447df17b3421_北京励控-五星售后(1).pdf'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2025\08\04\5ceeb27a-e384-4f58-a2f6-8adb3517fe15_北京励控-五星售后(1)(1).pdf'))">
      <SourceType>Package</SourceType>
      <SourceId>WatermarkCore</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/WatermarkCore</BasePath>
      <RelativePath>uploads/2025/08/04/5ceeb27a-e384-4f58-a2f6-8adb3517fe15_北京励控-五星售后(1)(1).pdf</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>f58nbr0wrl</Fingerprint>
      <Integrity>/T9PKiaDPuX0FHrsP2yQnMdzSy5igbrsR9QsDUQ7wAg=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\2025\08\04\5ceeb27a-e384-4f58-a2f6-8adb3517fe15_北京励控-五星售后(1)(1).pdf'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>