using Microsoft.EntityFrameworkCore;
using WatermarkCore.Models;

namespace WatermarkCore.Data
{
    /// <summary>
    /// 水印系统数据库上下文
    /// </summary>
    public class WatermarkDbContext : DbContext
    {
        public WatermarkDbContext(DbContextOptions<WatermarkDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// 文件夹表
        /// </summary>
        public DbSet<Folder> Folders { get; set; }

        /// <summary>
        /// 文件表
        /// </summary>
        public DbSet<FileItem> Files { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置文件夹实体
            modelBuilder.Entity<Folder>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.Id)
                    .HasMaxLength(36)
                    .IsRequired();

                entity.Property(e => e.Name)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.ParentId)
                    .HasMaxLength(36);

                entity.Property(e => e.Path)
                    .HasMaxLength(1000)
                    .IsRequired();

                entity.Property(e => e.Description)
                    .HasMaxLength(500);

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("datetime")
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("datetime")
                    .IsRequired();

                // 配置自引用关系
                entity.HasOne(e => e.Parent)
                    .WithMany(e => e.Children)
                    .HasForeignKey(e => e.ParentId)
                    .OnDelete(DeleteBehavior.Restrict);

                // 配置索引
                entity.HasIndex(e => e.ParentId);
                entity.HasIndex(e => e.Path);
                entity.HasIndex(e => new { e.ParentId, e.Name }).IsUnique();
            });

            // 配置文件实体
            modelBuilder.Entity<FileItem>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Id)
                    .HasMaxLength(36)
                    .IsRequired();

                entity.Property(e => e.Name)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.OriginalName)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.FileType)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.MimeType)
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.Extension)
                    .HasMaxLength(10)
                    .IsRequired();

                entity.Property(e => e.StoragePath)
                    .HasMaxLength(500)
                    .IsRequired();

                entity.Property(e => e.Hash)
                    .HasMaxLength(64);

                entity.Property(e => e.FolderId)
                    .HasMaxLength(36)
                    .IsRequired();

                entity.Property(e => e.Description)
                    .HasMaxLength(500);

                entity.Property(e => e.Status)
                    .HasMaxLength(20)
                    .IsRequired()
                    .HasDefaultValue("Normal");

                entity.Property(e => e.Tags)
                    .HasMaxLength(1000);

                entity.Property(e => e.Metadata)
                    .HasColumnType("text");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("datetime")
                    .IsRequired();

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("datetime")
                    .IsRequired();

                entity.Property(e => e.WatermarkedAt)
                    .HasColumnType("datetime");

                // 配置与文件夹的关系
                entity.HasOne(e => e.Folder)
                    .WithMany(e => e.Files)
                    .HasForeignKey(e => e.FolderId)
                    .OnDelete(DeleteBehavior.Cascade);

                // 配置索引
                entity.HasIndex(e => e.FolderId);
                entity.HasIndex(e => e.FileType);
                entity.HasIndex(e => e.Hash);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => new { e.FolderId, e.Name }).IsUnique();
            });

            // 种子数据 - 创建根文件夹
            modelBuilder.Entity<Folder>().HasData(
                new Folder
                {
                    Id = "root",
                    Name = "我的文件",
                    ParentId = null,
                    Path = "/",
                    Level = 0,
                    IsSystem = true,
                    Description = "系统根文件夹",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            );
        }
    }
}
